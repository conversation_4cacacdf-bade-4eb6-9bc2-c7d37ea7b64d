"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_schema_1 = require("./user.schema");
const sms_service_1 = require("./sms.service");
const jwt_1 = require("@nestjs/jwt");
let UserService = class UserService {
    userModel;
    smsService;
    jwtService;
    constructor(userModel, smsService, jwtService) {
        this.userModel = userModel;
        this.smsService = smsService;
        this.jwtService = jwtService;
    }
    async sendSmsCode(phone) {
        const code = '123456';
        console.log(`📱 发送验证码到 ${phone}: ${code}`);
        await this.userModel.updateOne({ phone }, { phone, smsCode: code }, { upsert: true });
        return true;
    }
    async verifyCode(phone, code) {
        let user = await this.userModel.findOne({ phone });
        if (!user) {
            user = await this.userModel.create({ phone, smsCode: code });
        }
        console.log(`✅ 用户 ${phone} 登录成功`);
        const payload = { sub: user._id, phone: user.phone };
        const token = this.jwtService.sign(payload);
        return { token, user: { id: user._id, phone: user.phone, nickname: user.nickname } };
    }
    async getUserProfile(userId) {
        const user = await this.userModel.findById(userId).select('-smsCode');
        if (!user)
            throw new Error('用户不存在');
        const today = new Date();
        const lastStudyDate = new Date(user.lastStudyDate);
        if (today.toDateString() !== lastStudyDate.toDateString()) {
            user.dailyStudyTimeUsed = 0;
            user.lastStudyDate = today;
            await user.save();
        }
        return user;
    }
    async updateProfile(userId, profileData) {
        const user = await this.userModel.findByIdAndUpdate(userId, { $set: profileData }, { new: true }).select('-smsCode');
        if (!user)
            throw new Error('用户不存在');
        return user;
    }
    async updateRestSettings(userId, restSettings) {
        const user = await this.userModel.findByIdAndUpdate(userId, { $set: { restSettings } }, { new: true }).select('-smsCode');
        if (!user)
            throw new Error('用户不存在');
        return user;
    }
    async getSubscriptionInfo(userId) {
        const user = await this.userModel.findById(userId).select('subscriptionPlan subscriptionExpiry isTrialUser dailyStudyTimeUsed');
        if (!user)
            throw new Error('用户不存在');
        const now = new Date();
        const isExpired = user.subscriptionExpiry < now;
        let dailyTimeLimit = 0;
        switch (user.subscriptionPlan) {
            case user_schema_1.SubscriptionPlan.TRIAL:
                dailyTimeLimit = isExpired ? 0 : 180;
                break;
            case user_schema_1.SubscriptionPlan.STANDARD:
                dailyTimeLimit = isExpired ? 0 : 180;
                break;
            case user_schema_1.SubscriptionPlan.PROFESSIONAL:
                dailyTimeLimit = isExpired ? 0 : 300;
                break;
        }
        return {
            subscriptionPlan: user.subscriptionPlan,
            subscriptionExpiry: user.subscriptionExpiry,
            isTrialUser: user.isTrialUser,
            isExpired,
            dailyTimeLimit,
            dailyStudyTimeUsed: user.dailyStudyTimeUsed,
            remainingTime: Math.max(0, dailyTimeLimit - user.dailyStudyTimeUsed)
        };
    }
    async updateDailyStudyTime(userId, additionalMinutes) {
        const user = await this.userModel.findById(userId);
        if (!user)
            throw new Error('用户不存在');
        const today = new Date();
        const lastStudyDate = new Date(user.lastStudyDate);
        if (today.toDateString() !== lastStudyDate.toDateString()) {
            user.dailyStudyTimeUsed = 0;
            user.lastStudyDate = today;
        }
        user.dailyStudyTimeUsed += additionalMinutes;
        await user.save();
        return user.dailyStudyTimeUsed;
    }
    async updateTokenUsage(userId, tokensUsed) {
        await this.userModel.findByIdAndUpdate(userId, { $inc: { totalTokensUsed: tokensUsed } });
    }
    async updateSubscription(userId, plan, expiryDate) {
        const user = await this.userModel.findByIdAndUpdate(userId, {
            $set: {
                subscriptionPlan: plan,
                subscriptionExpiry: expiryDate,
                isTrialUser: false
            }
        }, { new: true }).select('-smsCode');
        if (!user)
            throw new Error('用户不存在');
        return user;
    }
    async checkSubscriptionValid(userId) {
        const subscriptionInfo = await this.getSubscriptionInfo(userId);
        return !subscriptionInfo.isExpired && subscriptionInfo.remainingTime > 0;
    }
};
exports.UserService = UserService;
exports.UserService = UserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        sms_service_1.SmsService,
        jwt_1.JwtService])
], UserService);
//# sourceMappingURL=user.service.js.map