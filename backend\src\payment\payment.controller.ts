import { Controller, Post, Get, Body, HttpException, HttpStatus, UseGuards, Request, Query } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { PaymentService } from './payment.service';
import { UserService } from '../user/user.service';
import { SubscriptionPlan } from '../user/user.schema';

@Controller('payment')
export class PaymentController {
  constructor(
    private readonly paymentService: PaymentService,
    private readonly userService: UserService,
  ) {}

  /**
   * 获取订阅计划列表
   */
  @Get('plans')
  getSubscriptionPlans() {
    return this.paymentService.getSubscriptionPlans();
  }

  /**
   * 创建支付订单（开发环境直接成功）
   */
  @Post('create-order')
  @UseGuards(AuthGuard('jwt'))
  async createPaymentOrder(
    @Request() req: any,
    @Body() body: { plan: 'standard' | 'professional'; duration: number }
  ) {
    try {
      const userId = req.user.userId;
      const { plan, duration } = body;

      console.log(`💳 模拟支付：用户 ${userId} 购买 ${plan} 计划 ${duration} 个月`);

      // 开发环境：直接模拟支付成功，更新用户订阅
      await this.updateUserSubscription(userId, plan, duration);

      return {
        success: true,
        paymentUrl: '#', // 不需要跳转支付页面
        message: '开发环境：支付成功，订阅已激活'
      };
    } catch (e) {
      throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 支付宝回调通知
   */
  @Post('notify')
  async handlePaymentNotify(@Body() body: any) {
    try {
      const success = await this.paymentService.handlePaymentNotify(body);
      
      if (success) {
        // 解析回传参数更新用户订阅
        const orderInfo = JSON.parse(body.passbackParams);
        await this.updateUserSubscription(orderInfo.userId, orderInfo.plan, orderInfo.duration);
        
        return 'success';
      } else {
        return 'fail';
      }
    } catch (e) {
      console.error('Payment notify error:', e);
      return 'fail';
    }
  }

  /**
   * 查询支付状态
   */
  @Get('status')
  @UseGuards(AuthGuard('jwt'))
  async queryPaymentStatus(@Query('orderId') orderId: string) {
    try {
      if (!orderId) {
        throw new Error('订单号不能为空');
      }

      const result = await this.paymentService.queryPaymentStatus(orderId);
      return result;
    } catch (e) {
      throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * 支付成功页面回调
   */
  @Get('success')
  async paymentSuccess(@Query() query: any) {
    // 这里可以处理支付成功后的逻辑
    return {
      success: true,
      message: '支付成功',
      data: query
    };
  }

  /**
   * 更新用户订阅状态
   */
  private async updateUserSubscription(userId: string, plan: 'standard' | 'professional', duration: number) {
    try {
      const user = await this.userService.getUserProfile(userId);

      // 计算新的过期时间
      const now = new Date();
      const currentExpiry = user.subscriptionExpiry > now ? user.subscriptionExpiry : now;
      const newExpiry = new Date(currentExpiry.getTime() + duration * 30 * 24 * 60 * 60 * 1000); // 简化为30天/月

      // 更新用户订阅信息
      const subscriptionPlan = plan === 'standard' ? SubscriptionPlan.STANDARD : SubscriptionPlan.PROFESSIONAL;
      await this.userService.updateSubscription(userId, subscriptionPlan, newExpiry);

      console.log(`Updated subscription for user ${userId}: ${subscriptionPlan} until ${newExpiry}`);

    } catch (error) {
      console.error('Failed to update user subscription:', error);
      throw error;
    }
  }
}
