"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const payment_service_1 = require("./payment.service");
const user_service_1 = require("../user/user.service");
const user_schema_1 = require("../user/user.schema");
let PaymentController = class PaymentController {
    paymentService;
    userService;
    constructor(paymentService, userService) {
        this.paymentService = paymentService;
        this.userService = userService;
    }
    getSubscriptionPlans() {
        return this.paymentService.getSubscriptionPlans();
    }
    async createPaymentOrder(req, body) {
        try {
            const userId = req.user.userId;
            const { plan, duration } = body;
            console.log(`💳 模拟支付：用户 ${userId} 购买 ${plan} 计划 ${duration} 个月`);
            await this.updateUserSubscription(userId, plan, duration);
            return {
                success: true,
                paymentUrl: '#',
                message: '开发环境：支付成功，订阅已激活'
            };
        }
        catch (e) {
            throw new common_1.HttpException(e.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async handlePaymentNotify(body) {
        try {
            const success = await this.paymentService.handlePaymentNotify(body);
            if (success) {
                const orderInfo = JSON.parse(body.passbackParams);
                await this.updateUserSubscription(orderInfo.userId, orderInfo.plan, orderInfo.duration);
                return 'success';
            }
            else {
                return 'fail';
            }
        }
        catch (e) {
            console.error('Payment notify error:', e);
            return 'fail';
        }
    }
    async queryPaymentStatus(orderId) {
        try {
            if (!orderId) {
                throw new Error('订单号不能为空');
            }
            const result = await this.paymentService.queryPaymentStatus(orderId);
            return result;
        }
        catch (e) {
            throw new common_1.HttpException(e.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async paymentSuccess(query) {
        return {
            success: true,
            message: '支付成功',
            data: query
        };
    }
    async updateUserSubscription(userId, plan, duration) {
        try {
            const user = await this.userService.getUserProfile(userId);
            const now = new Date();
            const currentExpiry = user.subscriptionExpiry > now ? user.subscriptionExpiry : now;
            const newExpiry = new Date(currentExpiry.getTime() + duration * 30 * 24 * 60 * 60 * 1000);
            const subscriptionPlan = plan === 'standard' ? user_schema_1.SubscriptionPlan.STANDARD : user_schema_1.SubscriptionPlan.PROFESSIONAL;
            await this.userService.updateSubscription(userId, subscriptionPlan, newExpiry);
            console.log(`Updated subscription for user ${userId}: ${subscriptionPlan} until ${newExpiry}`);
        }
        catch (error) {
            console.error('Failed to update user subscription:', error);
            throw error;
        }
    }
};
exports.PaymentController = PaymentController;
__decorate([
    (0, common_1.Get)('plans'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PaymentController.prototype, "getSubscriptionPlans", null);
__decorate([
    (0, common_1.Post)('create-order'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "createPaymentOrder", null);
__decorate([
    (0, common_1.Post)('notify'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "handlePaymentNotify", null);
__decorate([
    (0, common_1.Get)('status'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __param(0, (0, common_1.Query)('orderId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "queryPaymentStatus", null);
__decorate([
    (0, common_1.Get)('success'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "paymentSuccess", null);
exports.PaymentController = PaymentController = __decorate([
    (0, common_1.Controller)('payment'),
    __metadata("design:paramtypes", [payment_service_1.PaymentService,
        user_service_1.UserService])
], PaymentController);
//# sourceMappingURL=payment.controller.js.map