// 检查服务状态
const http = require('http');

function checkService(port, name) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: port,
      path: '/',
      method: 'GET',
      timeout: 3000
    }, (res) => {
      console.log(`✅ ${name} 运行正常 (端口 ${port})`);
      resolve(true);
    });

    req.on('error', () => {
      console.log(`❌ ${name} 未运行 (端口 ${port})`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`⏰ ${name} 响应超时 (端口 ${port})`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function checkAll() {
  console.log('🔍 检查服务状态...\n');
  
  const frontendOk = await checkService(5173, '前端服务');
  const backendOk = await checkService(3002, '后端服务');
  
  console.log('\n📋 检查结果:');
  console.log(`前端 (http://localhost:5173): ${frontendOk ? '✅' : '❌'}`);
  console.log(`后端 (http://localhost:3002): ${backendOk ? '✅' : '❌'}`);
  
  if (frontendOk && backendOk) {
    console.log('\n🎉 所有服务运行正常！');
    console.log('可以访问: http://localhost:5173');
  } else {
    console.log('\n⚠️  请检查未运行的服务');
    console.log('启动命令:');
    if (!backendOk) console.log('  后端: cd backend && npm run dev');
    if (!frontendOk) console.log('  前端: cd frontend && npm run dev');
  }
}

checkAll();
