import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument, SubscriptionPlan, RestSettings } from './user.schema';
import { SmsService } from './sms.service';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class UserService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private readonly smsService: SmsService,
    private readonly jwtService: JwtService,
  ) {}

  // 生成验证码并发送
  async sendSmsCode(phone: string) {
    // 开发环境使用固定验证码
    const code = '123456';
    console.log(`📱 发送验证码到 ${phone}: ${code}`);

    // 跳过真实短信发送
    // await this.smsService.sendCode(phone, code);

    await this.userModel.updateOne({ phone }, { phone, smsCode: code }, { upsert: true });
    return true;
  }

  // 校验验证码并登录/注册
  async verifyCode(phone: string, code: string) {
    let user = await this.userModel.findOne({ phone });

    // 开发环境：任意验证码都能通过，如果用户不存在则创建
    if (!user) {
      user = await this.userModel.create({ phone, smsCode: code });
    }

    // 开发环境跳过验证码校验
    // if (!user || user.smsCode !== code) throw new Error('验证码错误');

    console.log(`✅ 用户 ${phone} 登录成功`);

    // 登录成功，生成长效token
    const payload = { sub: user._id, phone: user.phone };
    const token = this.jwtService.sign(payload);
    return { token, user: { id: user._id, phone: user.phone, nickname: user.nickname } };
  }

  // 获取用户完整信息
  async getUserProfile(userId: string) {
    const user = await this.userModel.findById(userId).select('-smsCode');
    if (!user) throw new Error('用户不存在');

    // 检查是否需要重置每日使用时长
    const today = new Date();
    const lastStudyDate = new Date(user.lastStudyDate);
    if (today.toDateString() !== lastStudyDate.toDateString()) {
      user.dailyStudyTimeUsed = 0;
      user.lastStudyDate = today;
      await user.save();
    }

    return user;
  }

  // 更新用户档案
  async updateProfile(userId: string, profileData: { nickname?: string; age?: number; grade?: string; gender?: string }) {
    const user = await this.userModel.findByIdAndUpdate(
      userId,
      { $set: profileData },
      { new: true }
    ).select('-smsCode');

    if (!user) throw new Error('用户不存在');
    return user;
  }

  // 更新休息设置
  async updateRestSettings(userId: string, restSettings: RestSettings) {
    const user = await this.userModel.findByIdAndUpdate(
      userId,
      { $set: { restSettings } },
      { new: true }
    ).select('-smsCode');

    if (!user) throw new Error('用户不存在');
    return user;
  }

  // 获取订阅信息
  async getSubscriptionInfo(userId: string) {
    const user = await this.userModel.findById(userId).select('subscriptionPlan subscriptionExpiry isTrialUser dailyStudyTimeUsed');
    if (!user) throw new Error('用户不存在');

    const now = new Date();
    const isExpired = user.subscriptionExpiry < now;

    // 根据订阅计划确定每日时长限制
    let dailyTimeLimit = 0;
    switch (user.subscriptionPlan) {
      case SubscriptionPlan.TRIAL:
        dailyTimeLimit = isExpired ? 0 : 180; // 试用期3小时
        break;
      case SubscriptionPlan.STANDARD:
        dailyTimeLimit = isExpired ? 0 : 180; // 标准版3小时
        break;
      case SubscriptionPlan.PROFESSIONAL:
        dailyTimeLimit = isExpired ? 0 : 300; // 专业版5小时
        break;
    }

    return {
      subscriptionPlan: user.subscriptionPlan,
      subscriptionExpiry: user.subscriptionExpiry,
      isTrialUser: user.isTrialUser,
      isExpired,
      dailyTimeLimit,
      dailyStudyTimeUsed: user.dailyStudyTimeUsed,
      remainingTime: Math.max(0, dailyTimeLimit - user.dailyStudyTimeUsed)
    };
  }

  // 更新每日使用时长
  async updateDailyStudyTime(userId: string, additionalMinutes: number) {
    const user = await this.userModel.findById(userId);
    if (!user) throw new Error('用户不存在');

    // 检查是否需要重置每日使用时长
    const today = new Date();
    const lastStudyDate = new Date(user.lastStudyDate);
    if (today.toDateString() !== lastStudyDate.toDateString()) {
      user.dailyStudyTimeUsed = 0;
      user.lastStudyDate = today;
    }

    user.dailyStudyTimeUsed += additionalMinutes;
    await user.save();

    return user.dailyStudyTimeUsed;
  }

  // 更新Token使用量
  async updateTokenUsage(userId: string, tokensUsed: number) {
    await this.userModel.findByIdAndUpdate(
      userId,
      { $inc: { totalTokensUsed: tokensUsed } }
    );
  }

  // 更新用户订阅
  async updateSubscription(userId: string, plan: SubscriptionPlan, expiryDate: Date) {
    const user = await this.userModel.findByIdAndUpdate(
      userId,
      {
        $set: {
          subscriptionPlan: plan,
          subscriptionExpiry: expiryDate,
          isTrialUser: false
        }
      },
      { new: true }
    ).select('-smsCode');

    if (!user) throw new Error('用户不存在');
    return user;
  }

  // 检查订阅是否有效
  async checkSubscriptionValid(userId: string): Promise<boolean> {
    const subscriptionInfo = await this.getSubscriptionInfo(userId);
    return !subscriptionInfo.isExpired && subscriptionInfo.remainingTime > 0;
  }
}
