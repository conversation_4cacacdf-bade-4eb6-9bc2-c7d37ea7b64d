import React, { useState } from 'react';
import { userAPI } from '../utils/api';

export default function Login() {
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [step, setStep] = useState<'input' | 'verify'>('input');
  const [loading, setLoading] = useState(false);
  const [msg, setMsg] = useState('');

  // 发送验证码
  const sendCode = async () => {
    setLoading(true);
    setMsg('');
    try {
      await userAPI.sendCode(phone);
      setStep('verify');
      setMsg('验证码已发送（开发环境固定为：123456）');
    } catch (e: any) {
      setMsg(e.message || '发送失败');
    }
    setLoading(false);
  };

  // 校验验证码
  const verifyCode = async () => {
    setLoading(true);
    setMsg('');
    try {
      const data: any = await userAPI.verifyCode(phone, code);
      if (data.token) {
        localStorage.setItem('token', data.token);
        setMsg('登录成功');
        window.location.reload();
      } else {
        setMsg('登录失败');
      }
    } catch (e: any) {
      setMsg(e.message || '网络错误');
    }
    setLoading(false);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="bg-white p-8 rounded-xl shadow-lg w-96 max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">AI作业监督员</h1>
          <p className="text-gray-600">智能学习监督，助力成长</p>
        </div>

        {step === 'input' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                手机号码
              </label>
              <input
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
                placeholder="请输入11位手机号"
                value={phone}
                onChange={e => setPhone(e.target.value)}
                maxLength={11}
                disabled={loading}
              />
            </div>
            <button
              className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              onClick={sendCode}
              disabled={loading || !/^\d{11}$/.test(phone)}
            >
              {loading ? '发送中...' : '获取验证码'}
            </button>
          </div>
        )}

        {step === 'verify' && (
          <div className="space-y-4">
            <div className="text-center">
              <p className="text-gray-700 mb-1">验证码已发送至</p>
              <p className="font-medium text-blue-600">{phone}</p>
              <p className="text-sm text-orange-600 mt-2">开发环境固定验证码：123456</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                验证码
              </label>
              <input
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-all text-center text-lg tracking-widest"
                placeholder="请输入验证码"
                value={code}
                onChange={e => setCode(e.target.value)}
                maxLength={6}
                disabled={loading}
              />
            </div>
            <button
              className="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              onClick={verifyCode}
              disabled={loading}
            >
              {loading ? '验证中...' : '登录/注册'}
            </button>
            <button
              className="w-full text-gray-500 py-2 hover:text-gray-700 transition-colors"
              onClick={() => setStep('input')}
              disabled={loading}
            >
              返回修改手机号
            </button>
          </div>
        )}

        {msg && (
          <div className={`mt-4 p-3 rounded-lg text-center text-sm ${
            msg.includes('成功') || msg.includes('已发送')
              ? 'bg-green-50 text-green-700 border border-green-200'
              : 'bg-red-50 text-red-700 border border-red-200'
          }`}>
            {msg}
          </div>
        )}
      </div>

      <div className="mt-8 text-center text-gray-500 text-sm">
        <p>© 2024 AI作业监督员 - 让学习更专注</p>
      </div>
    </div>
  );
}
