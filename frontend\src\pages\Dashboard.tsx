import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { userAPI, studySessionAPI, reportAPI } from '../utils/api';
import { Play, Pause, BarChart3, Settings, LogOut, Clock, Target, TrendingUp } from 'lucide-react';

interface UserProfile {
  nickname: string;
  age: number;
  subscriptionPlan: string;
  subscriptionExpiry: string;
  dailyTimeLimit: number;
  dailyStudyTimeUsed: number;
  remainingTime: number;
}

interface StudyOverview {
  today: {
    totalStudyTime: number;
    averageFocusRate: number;
    totalSessions: number;
  };
  total: {
    totalSessions: number;
    totalStudyTime: number;
    averageFocusRate: number;
  };
}

export default function Dashboard() {
  const { logout } = useAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [studyOverview, setStudyOverview] = useState<StudyOverview | null>(null);
  const [currentSession, setCurrentSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      const [profile, subscription, overview, session] = await Promise.all([
        userAPI.getProfile(),
        userAPI.getSubscription(),
        reportAPI.getOverview(),
        studySessionAPI.getCurrent(),
      ]);

      setUserProfile({ ...(profile as any), ...(subscription as any) });
      setStudyOverview(overview as any);
      setCurrentSession(session as any);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    window.location.reload();
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}小时${mins}分钟` : `${mins}分钟`;
  };

  const getSubscriptionPlanName = (plan: string) => {
    switch (plan) {
      case 'trial': return '试用版';
      case 'standard': return '标准版';
      case 'professional': return '专业版';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">AI作业监督员</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                欢迎，{userProfile?.nickname || '用户'}
              </span>
              <button
                onClick={handleLogout}
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <LogOut className="w-4 h-4 mr-1" />
                退出
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 订阅状态卡片 */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">订阅状态</h2>
              <p className="text-sm text-gray-600">
                {getSubscriptionPlanName(userProfile?.subscriptionPlan || '')} ·
                到期时间：{userProfile?.subscriptionExpiry ? new Date(userProfile.subscriptionExpiry).toLocaleDateString() : '未知'}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">今日剩余时长</p>
              <p className="text-2xl font-bold text-blue-600">
                {formatTime(userProfile?.remainingTime || 0)}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <div className="bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{
                  width: `${userProfile ? (userProfile.dailyStudyTimeUsed / userProfile.dailyTimeLimit) * 100 : 0}%`
                }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              已使用 {formatTime(userProfile?.dailyStudyTimeUsed || 0)} / {formatTime(userProfile?.dailyTimeLimit || 0)}
            </p>
          </div>
        </div>

        {/* 快速操作 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Link
            to="/monitor"
            className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              {currentSession ? (
                <Pause className="w-8 h-8 text-red-500" />
              ) : (
                <Play className="w-8 h-8 text-green-500" />
              )}
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  {currentSession ? '继续学习' : '开始学习'}
                </h3>
                <p className="text-sm text-gray-600">
                  {currentSession ? '有进行中的会话' : '开始新的学习会话'}
                </p>
              </div>
            </div>
          </Link>

          <Link
            to="/reports"
            className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <BarChart3 className="w-8 h-8 text-blue-500" />
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">学习报告</h3>
                <p className="text-sm text-gray-600">查看学习数据和分析</p>
              </div>
            </div>
          </Link>

          <Link
            to="/settings"
            className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <Settings className="w-8 h-8 text-gray-500" />
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">设置</h3>
                <p className="text-sm text-gray-600">个性化设置和偏好</p>
              </div>
            </div>
          </Link>

          <Link
            to="/subscription"
            className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <TrendingUp className="w-8 h-8 text-purple-500" />
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">订阅管理</h3>
                <p className="text-sm text-gray-600">升级或续费订阅</p>
              </div>
            </div>
          </Link>
        </div>

        {/* 今日统计 */}
        {studyOverview && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <Clock className="w-6 h-6 text-blue-500" />
                <h3 className="ml-2 text-lg font-semibold text-gray-900">今日学习</h3>
              </div>
              <p className="text-3xl font-bold text-blue-600 mt-2">
                {formatTime(studyOverview.today.totalStudyTime)}
              </p>
              <p className="text-sm text-gray-600">
                共 {studyOverview.today.totalSessions} 次会话
              </p>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <Target className="w-6 h-6 text-green-500" />
                <h3 className="ml-2 text-lg font-semibold text-gray-900">今日专注率</h3>
              </div>
              <p className="text-3xl font-bold text-green-600 mt-2">
                {studyOverview.today.averageFocusRate}%
              </p>
              <p className="text-sm text-gray-600">保持专注状态</p>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <TrendingUp className="w-6 h-6 text-purple-500" />
                <h3 className="ml-2 text-lg font-semibold text-gray-900">总计学习</h3>
              </div>
              <p className="text-3xl font-bold text-purple-600 mt-2">
                {studyOverview.total.totalSessions}
              </p>
              <p className="text-sm text-gray-600">
                累计 {formatTime(studyOverview.total.totalStudyTime)}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
